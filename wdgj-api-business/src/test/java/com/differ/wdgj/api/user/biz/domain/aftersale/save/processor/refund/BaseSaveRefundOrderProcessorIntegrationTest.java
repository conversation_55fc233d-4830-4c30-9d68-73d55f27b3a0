package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveAfterSaleOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core.BusinessGetRefundOrderResponseOrderItemGenerator;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * BaseSaveRefundOrderProcessor 集成测试
 * 基于 BusinessGetRefundOrderResponseOrderItemGenerator 生成测试数据
 * 直接依赖 Spring 和数据库，不使用桩对象
 *
 * <AUTHOR>
 * @date 2024/12/19 下午9:00
 */
public class BaseSaveRefundOrderProcessorIntegrationTest extends BaseAfterSaleOrderTest {

    //region 基础集成测试

    /**
     * 基础退货退款单保存集成测试
     * 测试完整的保存流程，包括所有插件逻辑
     */
    @Test
    public void testSaveRefundOrder_BasicIntegration() {
        // 生成随机测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("保存操作应该成功", result.isSuccess());
        
        // 打印测试信息
        printTestInfo("基础集成测试", orderItem, result);
    }

    /**
     * 批量退货退款单保存集成测试
     * 测试批量处理逻辑
     */
    @Test
    public void testSaveRefundOrder_BatchIntegration() {
        // 生成多个随机测试数据
        List<BusinessGetRefundOrderResponseOrderItem> orderItems = Arrays.asList(
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(),
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(),
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom()
        );
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(orderItems);
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("批量保存操作应该成功", result.isSuccess());
        Assert.assertNotNull("保存结果数据不应为空", result.getContent());
        
        // 打印批量测试信息
        System.out.println("=== 批量集成测试结果 ===");
        System.out.println("处理订单数量: " + orderItems.size());
        System.out.println("保存结果: " + (result.isSuccess() ? "成功" : "失败"));
        if (result.getContent() != null) {
            System.out.println("处理结果数量: " + result.getContent().size());
        }
        System.out.println("===================");
    }

    //endregion

    //region 不同售后类型测试

    /**
     * 退货退款单(JH_04)集成测试
     * 测试需要退货的售后单处理逻辑
     */
    @Test
    public void testSaveRefundOrder_RefundWithReturn_JH04() {
        // 创建退货退款单模板
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setRefundType(PolyRefundTypeEnum.JH_04.getCode());
        template.setRefundTypeDesc(PolyRefundTypeEnum.JH_04.getDescription());
        template.setHasGoodsReturn(true);
        
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = 
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("退货退款单保存应该成功", result.isSuccess());
        
        // 验证退货相关字段
        Assert.assertEquals("售后类型应为JH_04", PolyRefundTypeEnum.JH_04.getCode(), orderItem.getRefundType());
        Assert.assertTrue("应该需要退货", orderItem.getHasGoodsReturn());
        Assert.assertNotNull("应该有物流信息", orderItem.getLogisticName());
        
        printTestInfo("退货退款单(JH_04)测试", orderItem, result);
    }

    /**
     * 仅退款单(JH_03)集成测试
     * 测试不需要退货的售后单处理逻辑
     */
    @Test
    public void testSaveRefundOrder_RefundOnly_JH03() {
        // 创建仅退款单模板
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setRefundType(PolyRefundTypeEnum.JH_03.getCode());
        template.setRefundTypeDesc(PolyRefundTypeEnum.JH_03.getDescription());
        template.setHasGoodsReturn(false);
        
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = 
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("仅退款单保存应该成功", result.isSuccess());
        
        // 验证退款相关字段
        Assert.assertEquals("售后类型应为JH_03", PolyRefundTypeEnum.JH_03.getCode(), orderItem.getRefundType());
        
        printTestInfo("仅退款单(JH_03)测试", orderItem, result);
    }

    /**
     * 换货单(JH_05)集成测试
     * 测试换货单处理逻辑
     */
    @Test
    public void testSaveRefundOrder_Exchange_JH05() {
        // 创建换货单模板
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setRefundType(PolyRefundTypeEnum.JH_05.getCode());
        template.setRefundTypeDesc(PolyRefundTypeEnum.JH_05.getDescription());
        template.setHasGoodsReturn(true);
        
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = 
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        // 注意：某些平台可能不支持换货单，这里主要测试基础逻辑
        
        printTestInfo("换货单(JH_05)测试", orderItem, result);
    }

    //endregion

    //region 平台级特殊处理测试

    /**
     * 天猫国际直营平台特殊处理集成测试
     * 测试天猫国际直营的特殊逻辑：批量查询退款单信息、商品原始价格处理等
     */
    @Test
    public void testSaveRefundOrder_TmallGJZY_PlatformSpecific() {
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建天猫国际直营上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1212, PolyPlatEnum.BUSINESS_TmallGJZY);
        
        // 创建处理器（会自动选择TmallGJZYSaveRefundOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("天猫国际直营", orderItem, result);
    }

    /**
     * 得物平台特殊处理集成测试
     * 测试得物平台的特殊逻辑：订单类型判断、业务类型验证等
     */
    @Test
    public void testSaveRefundOrder_DeWu_PlatformSpecific() {
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setShopType(ShopTypeEnum.DEWU_BRAND_ZF.getPolyCode());
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);

        // 创建得物上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1446, PolyPlatEnum.BUSINESS_DeWu);
        
        // 创建处理器（会自动选择DeWuSaveRefundOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("得物", orderItem, result);
    }

    /**
     * 抖音超市平台特殊处理集成测试
     * 测试抖音超市平台的特殊逻辑
     */
    @Test
    public void testSaveRefundOrder_DouDianSupermarket_PlatformSpecific() {
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建抖音超市上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1360, PolyPlatEnum.BUSINESS_DouDianSupermarket);
        
        // 创建处理器（会自动选择DouDianSupermarketSaveRefundOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("抖音超市", orderItem, result);
    }

    /**
     * 京东平台特殊处理集成测试
     * 测试京东平台的特殊逻辑：店铺类型处理、售后类型转换等
     */
    @Test
    public void testSaveRefundOrder_JD_PlatformSpecific() {
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setShopType(ShopTypeEnum.JD_SOP.getPolyCode());
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建京东上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1356, PolyPlatEnum.BUSINESS_JD);
        
        // 创建处理器（会自动选择JDSaveRefundOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("京东", orderItem, result);
    }

    /**
     * 淘宝平台特殊处理集成测试
     * 测试淘宝平台的特殊逻辑：换货单过滤等
     */
    @Test
    public void testSaveRefundOrder_Taobao_PlatformSpecific() {
        // 生成测试数据（非换货单）
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setRefundType(PolyRefundTypeEnum.JH_04.getCode()); // 使用退货退款单，避免换货单被过滤
        BusinessGetRefundOrderResponseOrderItem orderItem = 
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建淘宝上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1001, PolyPlatEnum.BUSINESS_Taobao);
        
        // 创建处理器（会自动选择TaoBaoSaveRefundOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("淘宝", orderItem, result);
    }

    /**
     * 淘宝换货单过滤测试
     * 测试淘宝平台对换货单的过滤逻辑
     */
    @Test
    public void testSaveRefundOrder_Taobao_ExchangeOrderFilter() {
        // 生成换货单测试数据
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setRefundType(PolyRefundTypeEnum.JH_05.getCode()); // 换货单
        BusinessGetRefundOrderResponseOrderItem orderItem = 
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建淘宝上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1001, PolyPlatEnum.BUSINESS_Taobao);
        
        // 创建处理器（会自动选择TaoBaoSaveRefundOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果 - 淘宝应该过滤掉换货单
        Assert.assertNotNull("保存结果不应为空", result);
        // 注意：这里可能会失败或者被过滤，这是预期的行为
        
        System.out.println("=== 淘宝换货单过滤测试 ===");
        System.out.println("退款单号: " + orderItem.getRefundNo());
        System.out.println("售后类型: " + orderItem.getRefundType() + " (换货单)");
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        if (!result.isSuccess()) {
            System.out.println("失败原因: " + result.getMessage());
        }
        System.out.println("===================");
    }

    /**
     * 快手小店平台特殊处理集成测试
     * 测试快手小店的特殊逻辑：保价单处理等
     */
    @Test
    public void testSaveRefundOrder_KuaiShouShop_PlatformSpecific() {
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建快手小店上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1300, PolyPlatEnum.BUSINESS_KuaiShouShop);
        
        // 创建处理器（会自动选择KuaiShouShopSaveRefundOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));

        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);

        printPlatformTestInfo("快手小店", orderItem, result);
    }

    //endregion

    //region 业务场景测试

    /**
     * 高金额退款单集成测试
     * 测试大额退款的处理逻辑
     */
    @Test
    public void testSaveRefundOrder_HighAmountRefund() {
        // 创建高金额模板
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setTotalAmount(new BigDecimal("9999.99"));
        template.setRefundAmount(new BigDecimal("8888.88"));
        template.setPayAmount(new BigDecimal("9999.99"));
        
        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = 
            BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("高金额退款单保存应该成功", result.isSuccess());
        
        // 验证金额
        Assert.assertEquals("总金额应为9999.99", new BigDecimal("9999.99"), orderItem.getTotalAmount());
        Assert.assertEquals("退款金额应为8888.88", new BigDecimal("8888.88"), orderItem.getRefundAmount());
        
        printTestInfo("高金额退款单测试", orderItem, result);
    }

    /**
     * 多商品退款单集成测试
     * 测试包含多个商品的退款单处理逻辑
     */
    @Test
    public void testSaveRefundOrder_MultipleGoods() {
        // 生成包含多个商品的测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 确保有多个退款商品
        Assert.assertNotNull("退款商品列表不应为空", orderItem.getRefundGoods());
        Assert.assertFalse("退款商品列表不应为空", orderItem.getRefundGoods().isEmpty());
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = 
            SaveRefundOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("多商品退款单保存应该成功", result.isSuccess());
        
        System.out.println("=== 多商品退款单测试 ===");
        System.out.println("退款单号: " + orderItem.getRefundNo());
        System.out.println("商品数量: " + orderItem.getRefundGoods().size());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        orderItem.getRefundGoods().forEach(goods ->
                System.out.println("  商品: " + goods.getProductName() + ", 退款金额: " + goods.getRefundAmount()));
        System.out.println("==================");
    }

    /**
     * 特殊类型退款单集成测试
     * 测试已发货/未发货仅退款
     */
    @Test
    public void testSaveRefundOrder_SpecialType() {
        // 创建未发货仅退款模板
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setRefundType("JH_BEFORE_SEND_REFUND");

        // 生成测试数据
        BusinessGetRefundOrderResponseOrderItem orderItem =
                BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        // 确保类型正确
        Assert.assertEquals("JH_BEFORE_SEND_REFUND", template.getRefundType());

        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96230);

        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor =
            SaveRefundOrderFactory.createProcessor(context);

        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Collections.singletonList(orderItem));

        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("特殊类型退款单保存应该成功", result.isSuccess());

        System.out.println("=== 特殊类型退款单测试 ===");
        System.out.println("退款单号: " + orderItem.getRefundNo());
        System.out.println("商品数量: " + orderItem.getRefundGoods().size());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        orderItem.getRefundGoods().forEach(goods ->
                System.out.println("  商品: " + goods.getProductName() + ", 退款金额: " + goods.getRefundAmount()));
        System.out.println("==================");
    }

    //endregion

    //region 工具方法

    /**
     * 创建指定平台的上下文
     */
    private AfterSaleSaveContext getContextForPlatform(String memberName, int shopId, PolyPlatEnum platform) {
        AfterSaleSaveContext context = getContext(memberName, shopId);
        // 设置平台信息
        context.setPlat(platform);
        return context;
    }

    /**
     * 打印测试信息
     */
    private void printTestInfo(String testName, BusinessGetRefundOrderResponseOrderItem orderItem, 
                              SaveAfterSaleResult<List<SaveOrderResultComposite>> result) {
        System.out.println("=== " + testName + " ===");
        System.out.println("退款单号: " + orderItem.getRefundNo());
        System.out.println("平台订单号: " + orderItem.getPlatOrderNo());
        System.out.println("售后类型: " + orderItem.getRefundType() + " - " + orderItem.getRefundTypeDesc());
        System.out.println("总金额: " + orderItem.getTotalAmount());
        System.out.println("退款金额: " + orderItem.getRefundAmount());
        System.out.println("需要退货: " + orderItem.getHasGoodsReturn());
        System.out.println("物流公司: " + orderItem.getLogisticName());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        if (!result.isSuccess()) {
            System.out.println("失败原因: " + result.getMessage());
        }

        // 订单级结果
        if(result.isSuccess()){
            result.getContent().forEach(composite -> {
                SaveAfterSaleOrderResult orderResult = composite.convergedResults();
                String orderInfo = String.format("订单号: %s, 结果: %s, 详细信息：%s", orderResult.getAfterSaleNo(), orderResult.isSuccess() ? "成功" : "失败", orderResult.getMessage());
                System.out.println(orderInfo);
            });
        }

        System.out.println("===================");
    }

    /**
     * 打印平台测试信息
     */
    private void printPlatformTestInfo(String platformName, BusinessGetRefundOrderResponseOrderItem orderItem, 
                                     SaveAfterSaleResult<List<SaveOrderResultComposite>> result) {
        System.out.println("=== " + platformName + "平台特殊处理测试 ===");
        System.out.println("退款单号: " + orderItem.getRefundNo());
        System.out.println("平台订单号: " + orderItem.getPlatOrderNo());
        System.out.println("售后类型: " + orderItem.getRefundType());
        System.out.println("买家昵称: " + orderItem.getBuyerNick());
        System.out.println("商品名称: " + orderItem.getProductName());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        if (!result.isSuccess()) {
            System.out.println("失败原因: " + result.getMessage());
        }
        if (result.getContent() != null) {
            System.out.println("处理结果数量: " + result.getContent().size());
        }
        // 订单级结果
        if(result.isSuccess()){
            result.getContent().forEach(composite -> {
                SaveAfterSaleOrderResult orderResult = composite.convergedResults();
                String orderInfo = String.format("订单号: %s, 结果: %s, 详细信息：%s", orderResult.getAfterSaleNo(), orderResult.isSuccess() ? "成功" : "失败", orderResult.getMessage());
                System.out.println(orderInfo);
            });
        }
        System.out.println("========================");
    }

    //endregion
}
