package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor;

import com.alibaba.fastjson.JSON;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.order.PolyOrderStatusEnum;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.RdsTbTradeSendInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.TbTradeJdpResponseDto;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 淘宝Rds - 订单转换器单元测试
 * 参考RdsTbRefundConvertorTest的测试结构和模式
 *
 * <AUTHOR>
 * @date 2025/6/26 下午2:30
 */
@Ignore
public class RdsTbOrderConvertorTest {

    //region 订单状态转换测试

    /**
     * 测试 covertTbOrderStatus 方法 - 已知状态转换
     */
    @Test
    public void testCovertTbOrderStatus() {
        // 测试等待买家付款相关状态
        Assert.assertEquals(PolyOrderStatusEnum.WAITING_BUYER_PAYMENT,
                RdsTbOrderConvertor.covertTbOrderStatus("TRADE_NO_CREATE_PAY"));
        Assert.assertEquals(PolyOrderStatusEnum.WAITING_BUYER_PAYMENT,
                RdsTbOrderConvertor.covertTbOrderStatus("WAIT_BUYER_PAY"));
        Assert.assertEquals(PolyOrderStatusEnum.WAITING_BUYER_PAYMENT,
                RdsTbOrderConvertor.covertTbOrderStatus("PAY_PENDING"));
        Assert.assertEquals(PolyOrderStatusEnum.WAITING_BUYER_PAYMENT,
                RdsTbOrderConvertor.covertTbOrderStatus("WAIT_PRE_AUTH_CONFIRM"));

        // 测试等待卖家发货状态
        Assert.assertEquals(PolyOrderStatusEnum.WAITING_SELLER_SHIPMENT,
                RdsTbOrderConvertor.covertTbOrderStatus("WAIT_SELLER_SEND_GOODS"));

        // 测试等待买家确认收货状态
        Assert.assertEquals(PolyOrderStatusEnum.WAITING_BUYER_CONFIRM,
                RdsTbOrderConvertor.covertTbOrderStatus("WAIT_BUYER_CONFIRM_GOODS"));

        // 测试交易成功状态
        Assert.assertEquals(PolyOrderStatusEnum.TRADE_SUCCESS,
                RdsTbOrderConvertor.covertTbOrderStatus("TRADE_BUYER_SIGNED"));
        Assert.assertEquals(PolyOrderStatusEnum.TRADE_SUCCESS,
                RdsTbOrderConvertor.covertTbOrderStatus("TRADE_FINISHED"));

        // 测试交易关闭状态
        Assert.assertEquals(PolyOrderStatusEnum.TRADE_CLOSED,
                RdsTbOrderConvertor.covertTbOrderStatus("TRADE_CLOSED"));
        Assert.assertEquals(PolyOrderStatusEnum.TRADE_CLOSED,
                RdsTbOrderConvertor.covertTbOrderStatus("TRADE_CLOSED_BY_TAOBAO"));

        // 测试已锁定状态
        Assert.assertEquals(PolyOrderStatusEnum.LOCKED,
                RdsTbOrderConvertor.covertTbOrderStatus("PAID_FORBID_CONSIGN"));

        // 测试卖家部分发货状态
        Assert.assertEquals(PolyOrderStatusEnum.PARTIALLY_SHIPPED,
                RdsTbOrderConvertor.covertTbOrderStatus("SELLER_CONSIGNED_PART"));
    }

    /**
     * 测试 covertTbOrderStatus 方法 - 未知状态处理
     */
    @Test
    public void testCovertTbOrderStatusWithUnknownStatus() {
        // 测试未知状态
        Assert.assertEquals(PolyOrderStatusEnum.OTHER,
                RdsTbOrderConvertor.covertTbOrderStatus("UNKNOWN_STATUS"));
        Assert.assertEquals(PolyOrderStatusEnum.OTHER,
                RdsTbOrderConvertor.covertTbOrderStatus("INVALID_STATUS"));
        Assert.assertEquals(PolyOrderStatusEnum.OTHER,
                RdsTbOrderConvertor.covertTbOrderStatus(""));
    }

    /**
     * 测试 covertTbOrderStatus 方法 - 空值处理
     */
    @Test
    public void testCovertTbOrderStatusWithNull() {
        // 测试null值
        Assert.assertEquals(PolyOrderStatusEnum.OTHER,
                RdsTbOrderConvertor.covertTbOrderStatus(null));
    }

    //endregion

    //region convertTbTradeSendInfo 方法测试

    /**
     * 测试 convertTbTradeSendInfo 方法 - 正常场景（所有订单都有发货时间）
     */
    @Test
    public void testConvertTbTradeSendInfo_WithConsignTime_ShouldReturnCorrectSendInfo() {
        // 构建测试数据JSON字符串
        String jsonStr = "{\n" +
                "  \"trade_fullinfo_get_response\": {\n" +
                "    \"trade\": {\n" +
                "      \"tid\": 12345,\n" +
                "      \"consign_time\": \"2025-06-26 14:30:45\",\n" +
                "      \"orders\": {\n" +
                "        \"order\": [\n" +
                "          {\n" +
                "            \"oid\": 123456,\n" +
                "            \"consign_time\": \"2025-06-26 14:30:45\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"oid\": 789012,\n" +
                "            \"consign_time\": \"2025-06-26 15:30:45\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";

        // 将JSON字符串反序列化为TbTradeJdpResponseDto对象
        TbTradeJdpResponseDto tbTradeJdpResponseDto = JSON.parseObject(jsonStr, TbTradeJdpResponseDto.class);

        // 执行转换
        RdsTbTradeSendInfo result = RdsTbOrderConvertor.convertTbTradeSendInfo(tbTradeJdpResponseDto);

        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("交易ID应该正确", Long.valueOf(12345L), result.getTId());
        Assert.assertTrue("所有订单都已发货，bAllSend应该为true", result.getBAllSend());

        // 验证子订单发货状态
        Map<Long, Boolean> subOrderSendMap = result.getSubOrderSendMap();
        Assert.assertNotNull("子订单发货状态映射不应为空", subOrderSendMap);
        Assert.assertEquals("应该有2个子订单", 2, subOrderSendMap.size());
        Assert.assertTrue("订单123456应该已发货", subOrderSendMap.get(123456L));
        Assert.assertTrue("订单789012应该已发货", subOrderSendMap.get(789012L));
    }

    /**
     * 测试 convertTbTradeSendInfo 方法 - 部分订单没有发货时间
     */
    @Test
    public void testConvertTbTradeSendInfo_PartialConsignTime_ShouldReturnMixedSendInfo() {
        // 构建测试数据JSON字符串
        String jsonStr = "{\n" +
                "  \"trade_fullinfo_get_response\": {\n" +
                "    \"trade\": {\n" +
                "      \"tid\": 12345,\n" +
                "      \"consign_time\": \"2025-06-26 14:30:45\",\n" +
                "      \"orders\": {\n" +
                "        \"order\": [\n" +
                "          {\n" +
                "            \"oid\": 123456,\n" +
                "            \"consign_time\": \"2025-06-26 14:30:45\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"oid\": 789012\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";

        // 将JSON字符串反序列化为TbTradeJdpResponseDto对象
        TbTradeJdpResponseDto tbTradeJdpResponseDto = JSON.parseObject(jsonStr, TbTradeJdpResponseDto.class);

        // 执行转换
        RdsTbTradeSendInfo result = RdsTbOrderConvertor.convertTbTradeSendInfo(tbTradeJdpResponseDto);

        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("交易ID应该正确", Long.valueOf(12345L), result.getTId());
        Assert.assertFalse("部分订单未发货，bAllSend应该为false", result.getBAllSend());

        // 验证子订单发货状态
        Map<Long, Boolean> subOrderSendMap = result.getSubOrderSendMap();
        Assert.assertNotNull("子订单发货状态映射不应为空", subOrderSendMap);
        Assert.assertEquals("应该有2个子订单", 2, subOrderSendMap.size());
        Assert.assertTrue("订单123456应该已发货", subOrderSendMap.get(123456L));
        Assert.assertFalse("订单789012应该未发货", subOrderSendMap.get(789012L));
    }

    /**
     * 测试 convertTbTradeSendInfo 方法 - 所有订单都没有发货时间
     */
    @Test
    public void testConvertTbTradeSendInfo_NoConsignTime_ShouldReturnAllUnshipped() {
        // 构建测试数据JSON字符串
        String jsonStr = "{\n" +
                "  \"trade_fullinfo_get_response\": {\n" +
                "    \"trade\": {\n" +
                "      \"tid\": 12345,\n" +
                "      \"orders\": {\n" +
                "        \"order\": [\n" +
                "          {\n" +
                "            \"oid\": 123456\n" +
                "          },\n" +
                "          {\n" +
                "            \"oid\": 789012\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";

        // 将JSON字符串反序列化为TbTradeJdpResponseDto对象
        TbTradeJdpResponseDto tbTradeJdpResponseDto = JSON.parseObject(jsonStr, TbTradeJdpResponseDto.class);

        // 执行转换
        RdsTbTradeSendInfo result = RdsTbOrderConvertor.convertTbTradeSendInfo(tbTradeJdpResponseDto);

        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("交易ID应该正确", Long.valueOf(12345L), result.getTId());
        Assert.assertFalse("所有订单都未发货，bAllSend应该为false", result.getBAllSend());

        // 验证子订单发货状态
        Map<Long, Boolean> subOrderSendMap = result.getSubOrderSendMap();
        Assert.assertNotNull("子订单发货状态映射不应为空", subOrderSendMap);
        Assert.assertEquals("应该有2个子订单", 2, subOrderSendMap.size());
        Assert.assertFalse("订单123456应该未发货", subOrderSendMap.get(123456L));
        Assert.assertFalse("订单789012应该未发货", subOrderSendMap.get(789012L));
    }

    /**
     * 测试 convertTbTradeSendInfo 方法 - 空值处理
     */
    @Test
    public void testConvertTbTradeSendInfoWithNull() {
        // 测试null值
        RdsTbTradeSendInfo result1 = RdsTbOrderConvertor.convertTbTradeSendInfo(null);
        Assert.assertNull("null输入应该返回null", result1);

        // 测试空对象
        TbTradeJdpResponseDto entity2 = new TbTradeJdpResponseDto();
        RdsTbTradeSendInfo result2 = RdsTbOrderConvertor.convertTbTradeSendInfo(entity2);
        Assert.assertNull("空对象应该返回null", result2);

        // 测试空TradeFullInfoGetResponse
        TbTradeJdpResponseDto entity3 = new TbTradeJdpResponseDto();
        TbTradeJdpResponseDto.TradeFullInfoGetResponse tradeFullInfoGetResponse = new TbTradeJdpResponseDto.TradeFullInfoGetResponse();
        entity3.setTradeFullInfoGetResponse(tradeFullInfoGetResponse);
        RdsTbTradeSendInfo result3 = RdsTbOrderConvertor.convertTbTradeSendInfo(entity3);
        Assert.assertNull("空TradeFullInfoGetResponse应该返回null", result3);
    }

    //endregion

    //region 辅助方法

    /**
     * 创建带有发货时间的交易对象
     *
     * @param consignTime 发货时间
     * @return 交易对象
     */
    private TbTradeJdpResponseDto.Trade createTradeWithConsignTime(String consignTime) {
        TbTradeJdpResponseDto.Trade trade = new TbTradeJdpResponseDto.Trade();
        trade.setConsignTime(consignTime);
        return trade;
    }

    /**
     * 创建带有发货时间的订单对象
     *
     * @param oid         订单ID
     * @param consignTime 发货时间
     * @return 订单对象
     */
    private TbTradeJdpResponseDto.Order createOrderWithConsignTime(Long oid, String consignTime) {
        TbTradeJdpResponseDto.Order order = new TbTradeJdpResponseDto.Order();
        order.setOid(oid);
        order.setConsignTime(consignTime);
        return order;
    }

    //endregion
}
