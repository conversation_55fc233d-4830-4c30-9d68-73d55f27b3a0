package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.order.PolyOrderStatusEnum;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.RdsTbTradeSendInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.TbTradeJdpResponseDto;

import java.util.HashMap;
import java.util.Map;

/**
 * 淘宝Rds - 订单转换器
 *
 * <AUTHOR>
 * @date 2025/3/28 下午3:29
 */
public class RdsTbOrderConvertor {
    //region 常量
    /**
     * 售后订单退款状态
     */
    private static final Map<String, PolyOrderStatusEnum> tbOrderStatusMap = new HashMap<>();
    //endregion

    //region 静态初始化块
    static {
        //region 初始化-售后订单退款状态
        tbOrderStatusMap.put("TRADE_NO_CREATE_PAY", PolyOrderStatusEnum.WAITING_BUYER_PAYMENT);
        tbOrderStatusMap.put("WAIT_BUYER_PAY", PolyOrderStatusEnum.WAITING_BUYER_PAYMENT);
        tbOrderStatusMap.put("PAY_PENDING", PolyOrderStatusEnum.WAITING_BUYER_PAYMENT);
        tbOrderStatusMap.put("WAIT_PRE_AUTH_CONFIRM", PolyOrderStatusEnum.WAITING_BUYER_PAYMENT);
        tbOrderStatusMap.put("WAIT_SELLER_SEND_GOODS", PolyOrderStatusEnum.WAITING_SELLER_SHIPMENT);
        tbOrderStatusMap.put("WAIT_BUYER_CONFIRM_GOODS", PolyOrderStatusEnum.WAITING_BUYER_CONFIRM);
        tbOrderStatusMap.put("TRADE_BUYER_SIGNED", PolyOrderStatusEnum.TRADE_SUCCESS);
        tbOrderStatusMap.put("TRADE_FINISHED", PolyOrderStatusEnum.TRADE_SUCCESS);
        tbOrderStatusMap.put("TRADE_CLOSED", PolyOrderStatusEnum.TRADE_CLOSED);
        tbOrderStatusMap.put("TRADE_CLOSED_BY_TAOBAO", PolyOrderStatusEnum.TRADE_CLOSED);
        tbOrderStatusMap.put("PAID_FORBID_CONSIGN", PolyOrderStatusEnum.LOCKED);
        tbOrderStatusMap.put("SELLER_CONSIGNED_PART", PolyOrderStatusEnum.PARTIALLY_SHIPPED);
        //endregion
    }
    //endregion

    /**
     * 转换订单状态
     *
     * @param tbOrderStatus 淘宝订单状态
     * @return 菠萝派订单状态
     */
    public static PolyOrderStatusEnum covertTbOrderStatus(String tbOrderStatus) {
        if (tbOrderStatusMap.containsKey(tbOrderStatus)) {
            return tbOrderStatusMap.get(tbOrderStatus);
        }

        return PolyOrderStatusEnum.OTHER;
    }

    /**
     * 转换订单发货信息
     *
     * @param tbTradeJdpResponseDto 订单信息
     * @return 订单发货信息
     */
    public static RdsTbTradeSendInfo convertTbTradeSendInfo(TbTradeJdpResponseDto tbTradeJdpResponseDto) {
        if (tbTradeJdpResponseDto == null || tbTradeJdpResponseDto.getTradeFullInfoGetResponse() == null ||
                tbTradeJdpResponseDto.getTradeFullInfoGetResponse().getTrade() == null ||
                tbTradeJdpResponseDto.getTradeFullInfoGetResponse().getTrade().getOrders() == null) {
            return null;
        }
        // Rds订单数据
        TbTradeJdpResponseDto.Trade trade = tbTradeJdpResponseDto.getTradeFullInfoGetResponse().getTrade();

        // 构建商品级结果
        boolean isAllSend = true;
        Map<Long, Boolean> subOrderSendMap = new HashMap<>();
        for (TbTradeJdpResponseDto.Order order : trade.getOrders().getOrder()) {
            boolean isSend = order.getConsignTime() != null;
            if (!isSend) {
                isAllSend = false;
            }
            subOrderSendMap.put(order.getOid(), isSend);
        }

        // 构建订单级发货结果
        RdsTbTradeSendInfo rdsTbTradeSendInfo = new RdsTbTradeSendInfo();
        rdsTbTradeSendInfo.setTId(trade.getTid());
        rdsTbTradeSendInfo.setBAllSend(isAllSend);
        rdsTbTradeSendInfo.setSubOrderSendMap(subOrderSendMap);
        return rdsTbTradeSendInfo;
    }
}
