package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.batch.BatchQueryEncryptHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.batch.BatchQuerySysMatchHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.batch.BatchQueryReturnInfoHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.batch.TaoBaoBatchQueryOrderRdsInfoHandler;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.RdsTbTradeSendInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiEncryptTradeDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnInfoDO;

import java.util.List;

/**
 * 扩展已存在售后单信息
 *
 * <AUTHOR>
 * @date 2024/7/22 下午8:21
 */
public class DbAfterSaleOrderExtItem {
    /**
     * 退款单信息数据列表
     * 数据来源{@link BatchQueryReturnInfoHandle}
     */
    private ApiReturnInfoDO returnInfo;

    /**
     * 密文信息
     * 数据来源{@link BatchQueryEncryptHandle}
     */
    private ApiEncryptTradeDO apiEncryptTrade;

    /**
     * 售后商品对应商品匹配信息列表
     * 数据来源{@link BatchQuerySysMatchHandle}
     */
    private List<AfterSaleGoodsSysMatchItem> goodsSysMatch;

    //region get/set

    public ApiReturnInfoDO getReturnInfo() {
        return returnInfo;
    }

    public void setReturnInfo(ApiReturnInfoDO returnInfo) {
        this.returnInfo = returnInfo;
    }

    public List<AfterSaleGoodsSysMatchItem> getGoodsSysMatch() {
        return goodsSysMatch;
    }

    public void setGoodsSysMatch(List<AfterSaleGoodsSysMatchItem> goodsSysMatch) {
        this.goodsSysMatch = goodsSysMatch;
    }

    public ApiEncryptTradeDO getApiEncryptTrade() {
        return apiEncryptTrade;
    }

    public void setApiEncryptTrade(ApiEncryptTradeDO apiEncryptTrade) {
        this.apiEncryptTrade = apiEncryptTrade;
    }

    public RdsTbTradeSendInfo getTaoBaoTradeSendInfo() {
        return taoBaoTradeSendInfo;
    }

    public void setTaoBaoTradeSendInfo(RdsTbTradeSendInfo taoBaoTradeSendInfo) {
        this.taoBaoTradeSendInfo = taoBaoTradeSendInfo;
    }

    //endregion
}
