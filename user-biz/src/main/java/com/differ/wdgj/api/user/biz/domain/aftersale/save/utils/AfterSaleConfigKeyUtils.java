package com.differ.wdgj.api.user.biz.domain.aftersale.save.utils;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;

/**
 * 售后配置键工具类
 *
 * <AUTHOR>
 * @date 2024/7/15 上午11:52
 */
public class AfterSaleConfigKeyUtils {
    private AfterSaleConfigKeyUtils() {
    }

    /**
     * 是否关闭hashCode校验
     *
     * @param plat       平台
     * @param memberName 外部会员
     * @return 结果
     */
    public static boolean isNeedRetryMessage(PolyPlatEnum plat, String memberName) {
        String[] splitChars = {"@"};
        return ConfigKeyUtils.isActionApiMultipleMatchValue(ConfigKeyEnum.ISACTION_AFTERSALE_CLOSEHASHCODECHECK, splitChars, plat.toString(), memberName);
    }

    /**
     * 异常关键字是否需要重试
     *
     * @param message 关键字
     * @return 结果
     */
    public static boolean isNeedRetryMessage(String message) {
        return ConfigKeyUtils.isActionApi(ConfigKeyEnum.ISACTION_AFTERSALE_RETRYMESSAGE, message);
    }


    /**
     * 是否开启售后单保存api售后类型转换V2
     *
     * @param memberName 外部会员
     * @return 结果
     */
    public static boolean isActionApiAfterSaleTypeConvertV2(String memberName) {
        String[] splitChars = {"@"};
        return ConfigKeyUtils.isActionApi(ConfigKeyEnum.ISACTION_AFTERSALE_APIAFTERSALETYPECONVERTV2, memberName);
    }

}
