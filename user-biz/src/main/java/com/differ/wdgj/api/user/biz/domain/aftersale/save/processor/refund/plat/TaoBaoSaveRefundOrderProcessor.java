package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.IPerBatchProcessOrderHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.batch.TaoBaoBatchRefundPaySendHandler;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.enums.LogisticInterceptStatusEnum;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbRefundAutoInterceptStatusDto;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbRefundLogisticsInterceptDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 淘宝
 *
 * <AUTHOR>
 * @date 2025/4/9 下午4:37
 */
public class TaoBaoSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public TaoBaoSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 重写基类方法

    /**
     * 增加额外前置批量查询插件
     */
    @Override
    protected List<IPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> createPerBatchQueryHandles() {
        List<IPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> perBatchQueryHandles = super.createPerBatchQueryHandles();
        // 淘宝仅退款单处理订单发货情况(用于淘宝业务区分已发货/未发货仅退款)
        perBatchQueryHandles.add(new TaoBaoBatchRefundPaySendHandler(context));
        return perBatchQueryHandles;
    }

    /**
     * 前置过滤订单
     *
     * @param sourceOrder 原始售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();

        // 过滤换货单
        if (PolyRefundTypeEnum.JH_05.getCode().equalsIgnoreCase(ployOrder.getRefundType())) {
            return AfterSaleHandleResult.failed("淘宝退货退款单业务不保存换货单");
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 售后版本
        afterSaleOrder.setReserved1(ployOrder.getRefundVersion());
        afterSaleOrder.setRefundVersion(ployOrder.getRefundVersion());
        // 退款阶段
        afterSaleOrder.setRefundPhase(ployOrder.getRefundPhase());
        // 快递拦截
        AfterSaleHandleResult convertResult = convertLogisticsIntercept(afterSaleOrder, ployOrder);
        if (convertResult.isFailed()) {
            return convertResult;
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 转换商品级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param sourceGoods 原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult refundGoodsConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        // 基础数据
        BusinessGetRefundResponseRefundGoodInfo ployAfterSaleGoods = sourceGoods.getPloyRefundGoods();

        // 商品级金额赋值，无需考虑基类兼容逻辑，直接使用推送库返回数据
        if (ployAfterSaleGoods != null) {
            refundGoods.setPrice(ployAfterSaleGoods.getRefundAmount());
        }

        return GoodsConvertHandleResult.success();
    }
    //endregion

    //region 私有方法

    /**
     * 转换物流快速拦截信息
     *
     * @param afterSaleOrder 售后单信息
     * @param ployOrder      菠萝派售后单信息
     * @return 转换结果
     */
    private AfterSaleHandleResult convertLogisticsIntercept(ApiReturnListDO afterSaleOrder, BusinessGetRefundOrderResponseOrderItem ployOrder) {
        // 基础信息
        TbRefundLogisticsInterceptDto logisticsIntercept = ployOrder.getTbRefundLogisticsIntercept();
        if (logisticsIntercept != null) {
            // 快递是否自动拦截
            afterSaleOrder.setBexInterceptAuto(logisticsIntercept.getBexInterceptAuto());
            // 快递拦截出资方
            afterSaleOrder.setExInterceptInvestor(logisticsIntercept.getExInterceptInvestor());
            // 快递拦截状态
            List<TbRefundAutoInterceptStatusDto> interceptStatusList = logisticsIntercept.getInterceptStatusList();
            if (interceptStatusList != null && !interceptStatusList.isEmpty()) {
                List<String> refundAutoInterceptStatusStrs = new ArrayList<>();
                // 可能存在一单多包裹的情况；
                for (TbRefundAutoInterceptStatusDto tbRefundAutoInterceptStatus : interceptStatusList) {
                    StringBuilder exInterceptEnumListString = new StringBuilder();
                    LogisticInterceptStatusEnum status = LogisticInterceptStatusEnum.create(tbRefundAutoInterceptStatus.getLogisticInterceptEnum());
                    // 未知状态不处理
                    if (status == null) {
                        continue;
                    }

                    exInterceptEnumListString.append(String.format("%s:%s",
                            StringUtils.defaultString(tbRefundAutoInterceptStatus.getMailNo(), StringUtils.EMPTY),
                            status.getDescription()));

                    if (status.equals(LogisticInterceptStatusEnum.INTERCEPT_FAILED)) {
                        // 拦截失败时附带失败原因
                        exInterceptEnumListString.append(":").append(StringUtils.defaultString(tbRefundAutoInterceptStatus.getInterceptFailCode(), ""));
                    }

                    refundAutoInterceptStatusStrs.add(exInterceptEnumListString.toString());
                }

                // 物流单号A:拦截状态A[:失败原因A];物流单号B:拦截状态B[:失败原因B]
                if (CollectionUtils.isNotEmpty(refundAutoInterceptStatusStrs)) {
                    String refundAutoInterceptStatus = StringUtils.join(refundAutoInterceptStatusStrs, ";");
                    afterSaleOrder.setExInterceptEnum(refundAutoInterceptStatus);
                }
            }
        }

        return AfterSaleHandleResult.success();
    }
    //endregion
}
