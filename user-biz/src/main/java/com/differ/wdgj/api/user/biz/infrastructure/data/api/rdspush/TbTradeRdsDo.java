package com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush;

import java.time.LocalDateTime;

/**
 * 淘宝推送库 - 订单单数据实体
 *
 * <AUTHOR>
 * @date 2025/6/26 10:50
 */
public class TbTradeRdsDo {
    /**
     * 订单ID
     */
    private Long tId;

    /**
     * 退款状态
     */
    private String status;

    /**
     * 类型
     */
    private String type;

    /**
     * 卖家昵称
     */
    private String sellerNick;

    /**
     * 买家昵称
     */
    private String buyerNick;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;

    /**
     * JDP哈希码
     */
    private String jdpHashcode;

    /**
     * JDP响应
     */
    private String jdpResponse;

    /**
     * JDP创建时间
     */
    private LocalDateTime jdpCreated;

    /**
     * JDP修改时间
     */
    private LocalDateTime jdpModified;

    public Long gettId() {
        return tId;
    }

    public void settId(Long tId) {
        this.tId = tId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public LocalDateTime getCreated() {
        return created;
    }

    public void setCreated(LocalDateTime created) {
        this.created = created;
    }

    public LocalDateTime getModified() {
        return modified;
    }

    public void setModified(LocalDateTime modified) {
        this.modified = modified;
    }

    public String getJdpHashcode() {
        return jdpHashcode;
    }

    public void setJdpHashcode(String jdpHashcode) {
        this.jdpHashcode = jdpHashcode;
    }

    public String getJdpResponse() {
        return jdpResponse;
    }

    public void setJdpResponse(String jdpResponse) {
        this.jdpResponse = jdpResponse;
    }

    public LocalDateTime getJdpCreated() {
        return jdpCreated;
    }

    public void setJdpCreated(LocalDateTime jdpCreated) {
        this.jdpCreated = jdpCreated;
    }

    public LocalDateTime getJdpModified() {
        return jdpModified;
    }

    public void setJdpModified(LocalDateTime jdpModified) {
        this.jdpModified = jdpModified;
    }
}
