package com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;

/**
 * api售后单类型
 *
 * <AUTHOR>
 * @date 2024/7/15 下午6:46
 */
public enum ApiAfterSaleTypeEnum implements CodeEnum {
    /**
     * 表示所有售后单（默认值）。
     */
    REFUND_ALL("所有售后单(默认值)", "REFUND_ALL"),

    /**
     * 表示换货单。
     */
    EXCHANGE("换货单", "EXCHANGE"),

    /**
     * 表示退货单。
     */
    REFUND_GOODS("退货单", "REFUND_GOODS"),

    /**
     * 表示仅退款单。
     */
    REFUND_PAY("仅退款单", "REFUND_PAY"),

    /**
     * 表示未发货退款单。
     */
    REFUND_PAY_NOT_SEND("未发货仅退款单", "REFUND_PAY_NOT_SEND"),

    /**
     * 表示已发货退款单。
     */
    REFUND_PAY_SEND("已发货退款单", "REFUND_PAY_SEND"),

    /**
     * 表示在途退款单。
     */
    REFUND_PAY_TRANSIT("在途退款单", "REFUND_PAY_TRANSIT"),

    /**
     * 表示已收货退款单。
     */
    REFUND_PAY_RECEIVE("已收货退款单", "REFUND_PAY_RECEIVE"),

    /**
     * 表示退货退款单。
     */
    REFUND("退货退款单", "REFUND"),

    /**
     * 表示价保售后单。
     */
    REFUND_BJ("价保售后单", "REFUND_BJ"),

    /**
     * 表示维修单
     */
    REPAIR("维修单","REPAIR"),

    /**
     * 表示补寄单。
     */
    REFUND_SUPPLEMENT("补寄单", "REFUND_SUPPLEMENT"),

    /**
     * 表示商家退款。
     */
    REFUND_MERCHANT("商家退款", "REFUND_MERCHANT");

    //region 常量
    private final String description;
    private final String code;
    //endregion

    //region 构造
    /**
     * 构造函数初始化枚举常量。
     *
     * @param description 枚举的描述。
     * @param code        枚举的代码。
     */
    ApiAfterSaleTypeEnum(String description, String code) {
        this.description = description;
        this.code = code;
    }
    //endregion

    //region 公共方法

    /**
     * 获取枚举的描述。
     *
     * @return 描述。
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取枚举的代码。
     *
     * @return 代码。
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static ApiAfterSaleTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, ApiAfterSaleTypeEnum.class, EnumConvertType.CODE);
    }
    //endregion
}
